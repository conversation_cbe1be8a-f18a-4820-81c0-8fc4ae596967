PRODUCTION-READY SORUNLARI İÇİN PROMPT LİSTESİ
==============================================

PROJE DURUMU: 1.5 yıldır tek spor salonunda sorunsuz çalışıyor. Hedef: Denizli'deki tüm spor salonlarına yayılmak için "tak-çalıştır" sistemi oluşturmak.

⚠️ ÖNEM SIRASI: En kritik sorundan başlayarak sıralanmıştır. 1'den başlayın!

🔥 HAYATI ÖNCELİK - SİSTEM ÇÖKMEDEN ÖNCE (1-5)

PROMPT 1: MULTI-TENANT TENANT ISOLATION GÜVENLİĞİ (HAYATİ!)
--------------------------------------------------------
Sistemim 1.5 yıldır tek salonda sorunsuz çalışıyor ama şimdi çoklu salon yapısına geçecek. Tek veritabanında CompanyID ile tenant separation yapacağım. Şu anda her API isteğinde hangi salon için geldiğini anlama mekanizmam yok. Bir salon diğer salonun verilerini görebilir - bu felaket olur!

Şu anda kurduğum sistem hakkında bilgim var ama multi-tenant data isolation konusunda deneyimim yok. Tenant isolation nasıl çalışır, neden kritik? Row-level security nedir? CompanyContext pattern'i nasıl implement edilir? Data leakage nasıl önlenir? Tenant resolution nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 2: MANUEL ENVIRONMENT MANAGEMENT VE CONNECTION STRING GÜVENLİĞİ (HAYATİ!)
------------------------------------------------------------------------
3 <USER> <GROUP>ıyorum: 1) Local test, 2) Staging (staging.gymkod.com), 3) Production (admin.gymkod.com).
Her environment geçişinde GymContext.cs'deki connection string'i manuel değiştiriyorum:
- Staging: "Server=localhost;User Id=sa;Password=************;Database=Staging;Trusted_Connection=false;Encrypt=False"
- Production: "Server=localhost;User Id=sa;Password=************;Database=GymProject;Trusted_Connection=false;Encrypt=False"
Ayrıca Program.cs'yi de manuel düzenliyorum. Bu süreç hata riski yüksek, ölçeklenebilir değil ve şifreler kod içinde görünüyor!

Şu anda kurduğum sistem hakkında bilgim var ama environment-based configuration konusunda deneyimim yok.
Environment variables nasıl çalışır? appsettings.{Environment}.json pattern'i nedir?
Configuration management nasıl otomatikleştirilir? DevOps best practices neler? GitHub'da şifre nasıl gizlenir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 3: ENVIRONMENT BAZLI CORS VE GÜVENLİK AÇIĞI (HAYATİ!)
----------------------------------------------------------
Program.cs'de AllowAnyOrigin() kullanıyorum - bu herhangi bir website'in API'mi kullanabileceği anlamına geliyor!
3 environment'ım var: Local, Staging (staging.gymkod.com), Production (admin.gymkod.com).
Her environment için farklı CORS policy gerekiyor ama şu anda hepsinde aynı kod var. Manuel olarak Program.cs'yi değiştiriyorum.

Şu anda kurduğum sistem hakkında bilgim var ama environment-based CORS konusunda deneyimim yok.
Environment bazlı CORS nasıl yapılır? staging.gymkod.com ve admin.gymkod.com için
güvenli CORS policy'si nasıl tasarlanır? Development vs Production CORS farkları neler?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 4: DI LIFETIME MEMORY LEAK RİSKİ (HAYATİ!)
-----------------------------------------------
AutofacBusinessModule'de DAL sınıfları SingleInstance olarak kayıtlı. Bu yaklaşım memory leak'e neden olabilir ve çoklu salon ortamında sistem çökebilir. Ayrıca DbContext lifetime'ı ile uyumsuz.

Şu anda kurduğum sistem hakkında bilgim var ama DI lifetime management konusunda deneyimim yok. SingleInstance vs Scoped vs Transient farkları neler? Memory leak nasıl oluşur? DbContext lifetime neden önemli? Multi-tenant ortamda DI nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 5: MOBİL APP GÜVENLİK VE NETWORK AÇIĞI (HAYATİ!)
-----------------------------------------------------
Mobile app'te baseUrl = 'http://*************:5165/api/' hard-coded IP var ve HTTP kullanıyor (HTTPS değil). Production'da bu IP çalışmayacak ve güvenlik riski oluşturuyor. Ayrıca network kesintilerinde turnike çalışmayacak, offline mode yok.

Şu anda kurduğum sistem hakkında bilgim var ama mobile app network management konusunda deneyimim yok. Environment-based API URL nasıl yapılır? HTTPS nasıl implement edilir? Offline mode nasıl çalışır? Mobile app security best practices neler?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 6: DATABASE İNDEX EKSİKLİĞİ PERFORMANS ÇÖKÜŞÜ (HAYATİ!)
------------------------------------------------------------
Çoklu salon ortamında her sorgu CompanyID ile filtrelenmeli ama mevcut indexlerde CompanyID composite indexleri eksik olabilir. 50+ salon olunca sorgular çok yavaşlayacak, sistem kullanılamaz hale gelecek.

Şu anda kurduğum sistem hakkında bilgim var ama database indexing konusunda deneyimim yok. Index nedir, neden önemli? Composite index nasıl çalışır? Multi-tenant query pattern'leri için hangi indexler şart? Query performance nasıl ölçülür?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 3.5: MANUEL DEPLOYMENT SÜRECİ OTOMASYONU (HAYATİ!)
--------------------------------------------------------
Şu anda Local → Staging → Production geçişlerinde manuel kod değiştiriyorum. GymContext.cs'deki connection string'leri, Program.cs'deki CORS ayarlarını elle değiştiriyorum. Bu süreç hata riski yüksek ve 1000 salon için ölçeklenebilir değil. Otomatik build, test, deploy pipeline'ı gerekiyor.

Şu anda kurduğum sistem hakkında bilgim var ama CI/CD, automated deployment konusunda deneyimim yok.
Environment-based configuration nasıl çalışır? CI/CD pipeline nedir? GitHub Actions, Azure DevOps gibi araçlar nasıl kullanılır? Blue-green deployment nedir? Rollback stratejisi nasıl olur?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

🚨 KRİTİK ÖNCELİK - SİSTEM STABIL OLMASI İÇİN (7-12)
=================================================

PROMPT 7: API INPUT VALİDATİON VE GÜVENLİK AÇIKLARI (HAYATİ!)
-----------------------------------------------------------
Controller'larda model validation eksik. MemberController, MembershipController gibi endpoint'lerde input validation yok. SQL injection, XSS attack riski var. FluentValidation var ama controller seviyesinde uygulanmamış.

Şu anda kurduğum sistem hakkında bilgim var ama API security konusunda deneyimim yok. Input validation nasıl yapılır? SQL injection nasıl önlenir? XSS protection nedir? Model binding security nasıl sağlanır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 8: JWT TOKEN GÜVENLİK SORUNLARI
------------------------------------
SecurityKey appsettings.json'da açık, token rotation yok. 2 dakikalık token süresi çok kısa - salon çalışanları sürekli login olmak zorunda kalacak. Çoklu salon ortamında kullanıcı deneyimi kritik.

Şu anda kurduğum sistem hakkında bilgim var ama JWT security best practices konusunda deneyimim yok. JWT token lifecycle nasıl çalışır? Refresh token rotation nedir? Token süresi nasıl belirlenir? SecurityKey nasıl güvenli tutulur?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 9: DATABASE FOREIGN KEY VE CONSTRAINT EKSİKLİKLERİ
-------------------------------------------------------
Migration'larda cascade delete kuralları eksik. Orphan record riski var. Data integrity sorunları olabilir. Foreign key constraint'ler tam değil.

Şu anda kurduğum sistem hakkında bilgim var ama database constraints konusunda deneyimim yok. Foreign key constraints nedir? Cascade delete nasıl çalışır? Data integrity nasıl sağlanır? Orphan record nedir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 10: RASPBERRY PI RATE LİMİTİNG SORUNU
------------------------------------------
Mevcut rate limiting 10 saniyede 1 istek ama yoğun saatlerde çoklu giriş olabilir. Raspberry Pi'lar sürekli /api/member/scannumber endpoint'ine istek atacak. Rate limiting çok kısıtlayıcı olunca turnike çalışmayacak.

Şu anda kurduğum sistem hakkında bilgim var ama IoT device rate limiting konusunda deneyimim yok. IoT cihazları için rate limiting neden farklı? Burst traffic nasıl handle edilir? Device-based rate limiting nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 11: MULTI-TENANT CACHE MEMORY SORUNU
-----------------------------------------
MaxCacheSize 10.000 ama 50+ salon olunca yetersiz kalacak. Her salon farklı data pattern'ine sahip olacak. Cache isolation sağlanmazsa bir salon diğerinin cache'ini bozabilir.

Şu anda kurduğum sistem hakkında bilgim var ama multi-tenant cache management konusunda deneyimim yok. Cache isolation nasıl çalışır? Memory pressure nasıl yönetilir? Cache eviction policy nedir? Redis vs in-memory cache farkları neler?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 12: N+1 QUERY VE PERFORMANCE BOTTLENECK'LER
------------------------------------------------
EfUserDal'da N+1 query problemi var. Eager loading eksik. Database connection pooling yok. 50+ salon olunca performance çökecek.

Şu anda kurduğum sistem hakkında bilgim var ama query optimization konusunda deneyimim yok. N+1 query problemi nedir? Eager loading nasıl yapılır? Connection pooling nasıl çalışır? Query performance nasıl optimize edilir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 8: PRODUCTION DATABASE İNDEX STRATEJİSİ
--------------------------------------------
Tek salonda query performance sorun değildi ama şimdi çoklu salon ortamında CompanyID filtreleri kritik olacak. Her sorgu CompanyID ile filtrelenmeli. Mevcut indexlerde CompanyID composite indexleri eksik olabilir. Multi-tenant query pattern'leri için hangi indexler şart? Performance monitoring nasıl yapılmalı? Index maintenance stratejisi nedir?

⚡ KISA VADELİ ÖNCELİK - ÖLÇEKLENEBİLİRLİK

PROMPT 9: SALON ÇALIŞANLARI İÇİN FRONTEND OPTİMİZASYONU
------------------------------------------------------
Tek salonda bundle size sorun değildi ama şimdi farklı internet hızlarında çalışan salonlar olacak. 15MB bundle çok büyük, yavaş internet'te açılmayabilir. Salon çalışanları sabırsız, hızlı açılması gerekiyor. Lazy loading, code splitting nasıl implement edilmeli? Salon bazlı feature'lar nasıl ayrılmalı? PWA özellikleri gerekli mi?

PROMPT 10: UZUN SÜRE AÇIK KALACAK SİSTEM İÇİN MEMORY YÖNETİMİ
-----------------------------------------------------------
Tek salonda günlük restart sorun değildi ama şimdi salonlar sistemi 7/24 açık tutacak. Memory leak'ler zamanla birikecek. Salon çalışanları teknik bilgili değil, restart yapamayacaklar. Frontend'te subscription cleanup, backend'te memory monitoring nasıl yapılmalı? Otomatik memory cleanup stratejisi nedir?

PROMPT 11: RASPBERRY PI SETUP VE DONANIM YÖNETİMİ
-----------------------------------------------
Şimdi her yeni salon için Raspberry Pi kurulumu, QR kod okuyucu konfigürasyonu, kart sistemi entegrasyonu manuel yapıyorum. Bu ölçeklenemez. Raspberry Pi'ların otomatik konfigürasyonu, remote management, firmware update'leri nasıl yapılmalı? QR vs kart sistemi seçimi nasıl yönetilmeli? Device monitoring ve troubleshooting nasıl olmalı? Salon sahibi teknik bilgili değilse nasıl destek verilmeli?

PROMPT 12: QR vs KART SİSTEMİ FİYATLANDIRMA STRATEJİSİ
--------------------------------------------------
Tek salonda QR sistem yetiyordu ama şimdi salon sahipleri "QR mu kart mı?" diye soracak, ikisini de isteyebilir. QR sistemi (sadece Raspberry Pi), kart sistemi (ek donanım), hibrit sistem (her ikisi) için farklı fiyatlandırma gerekiyor. Donanım maliyetleri, kurulum, maintenance nasıl fiyatlandırılmalı? Feature-based pricing nasıl yapılmalı? Upgrade/downgrade senaryoları nasıl yönetilmeli?

PROMPT 13: SALON PERFORMANS MONİTORİNG VE ANALİTİK
------------------------------------------------
Tek salonda manuel takip yetiyordu ama şimdi tüm salonları aynı anda izlemem gerekiyor. Hangi salon ne kadar kullanıyor, performance sorunları nerede, hangi feature'lar popüler? Salon dashboard'u nasıl olmalı? Admin panel'de tüm salonları nasıl izleyebilirim? Usage analytics nasıl toplanmalı?

PROMPT 14: KOLAY DEPLOYMENT VE GÜNCELLEME SİSTEMİ
-----------------------------------------------
Tek salonda manuel deployment sorun değildi ama şimdi tüm salonlara aynı anda güncelleme göndermem gerekecek. Zero-downtime deployment, rollback capability, A/B testing gerekiyor. Docker containerization nasıl yapılmalı? CI/CD pipeline nasıl kurulmalı? Blue-green deployment stratejisi nedir?

📊 ORTA VADELİ ÖNCELİK - OPERASYONEL SÜRDÜRÜLEBİLİRLİK

PROMPT 15: 7/24 UPTIME VE HEALTH MONİTORİNG
-----------------------------------------
Tek salonda downtime sorun değildi ama şimdi salonlar 7/24 çalışacak. Sistem çökerse salon işleri durur. Proactive monitoring, alerting, auto-healing gerekiyor. Health check'ler nasıl kurulmalı? Database, cache, API health nasıl izlenmeli? Incident response planı nedir? Uptime SLA nasıl garanti edilir?

PROMPT 16: CENTRALIZED LOGGİNG VE ERROR TRACKİNG
----------------------------------------------
Tek salonda local log'lar yetiyordu ama şimdi tüm salonların log'larını merkezi izlemem gerekiyor. Hangi salonda ne hata oluyor, performance sorunları nerede? Structured logging, correlation ID, centralized log management nasıl kurulmalı? ELK Stack, Application Insights'tan hangisi uygun? Error alerting nasıl çalışmalı?

PROMPT 17: OTOMATIK BACKUP VE DISASTER RECOVERY
---------------------------------------------
Tek salonda manuel backup yetiyordu ama şimdi her salon için otomatik backup gerekiyor. Salon verileri kaybolursa iş durur. Point-in-time recovery, cross-region backup, disaster recovery planı nasıl olmalı? Backup retention policy nedir? RTO/RPO değerleri nasıl belirlenmeli? Backup test stratejisi nedir?

PROMPT 18: SECURITY COMPLIANCE VE AUDIT
-------------------------------------
Tek salonda güvenlik sorun değildi ama şimdi müşteri verileri için KVKK compliance gerekiyor. Data encryption, audit logging, access control, privacy policy implementation nasıl yapılmalı? GDPR/KVKV requirements neler? Security audit nasıl geçilir? Penetration testing gerekli mi?

PROMPT 19: LOAD BALANCİNG VE AUTO-SCALİNG
---------------------------------------
Tek instance ile başlayacağım ama büyüdükçe horizontal scaling gerekecek. Peak saatlerde (akşam 18-21) tüm salonlar aynı anda yoğun kullanım yapacak. Load balancer, auto-scaling, session management nasıl kurulmalı? Azure/AWS'de hangi servisler kullanılmalı? Cost optimization nasıl yapılmalı?

PROMPT 20: PERFORMANCE OPTİMİZASYON VE QUERY TUNİNG
------------------------------------------------
Tek salonda query performance sorun değildi ama şimdi çoklu salon ortamında N+1 query, eager loading, pagination sorunları çıkabilir. Database query optimization, Entity Framework best practices, connection pooling nasıl yapılmalı? Query execution plan analysis nasıl yapılır? Performance bottleneck'leri nasıl tespit edilir?

🎯 UZUN VADELİ ÖNCELİK - SATIŞ HAZIRLIĞI

PROMPT 21: IOT CİHAZ YÖNETİMİ VE REMOTE SUPPORT
----------------------------------------------
Tek salonda fiziksel erişim kolaydı ama şimdi Denizli'deki tüm salonlarda Raspberry Pi'lar olacak. Remote monitoring, firmware update, troubleshooting, device health check nasıl yapılmalı? Cihaz çökerse salon girişleri durur. IoT device management platform gerekli mi? Remote access nasıl güvenli yapılır? Preventive maintenance nasıl planlanır?

PROMPT 22: FİZİKSEL KURULUM VE SALON ONBOARDING
--------------------------------------------
Yeni salon nasıl sisteme dahil edilecek? Raspberry Pi kurulumu, QR kod okuyucu montajı, kart sistemi entegrasyonu, network konfigürasyonu, staff training nasıl yapılmalı? Salon sahibi teknik bilgili değilse kurulum nasıl basitleştirilmeli? Remote support nasıl verilmeli? Troubleshooting guide nasıl hazırlanmalı? Go-live checklist'i nedir?

PROMPT 23: BILLING VE PAYMENT INTEGRATION
----------------------------------------
Şimdi aylık/yıllık subscription model gerekiyor. Otomatik billing, payment processing, invoice generation, dunning management nasıl yapılmalı? Stripe, PayTR gibi payment gateway'lerden hangisi uygun? Pricing tiers nasıl yönetilmeli? Churn prevention stratejisi nedir?

PROMPT 24: LEGAL VE COMPLIANCE HAZIRLIĞI
---------------------------------------
SaaS business için terms of service, privacy policy, SLA agreements gerekiyor. KVKV compliance, data processing agreements, liability limitations nasıl hazırlanmalı? Legal review süreci nedir? Compliance audit nasıl geçilir? Insurance requirements neler?

PROMPT 25: DONANIM VE YAZILIM ENTEGRASYON ANALİTİĞİ
-------------------------------------------------
Salon sahipleri için QR vs kart kullanım istatistikleri, cihaz performance metrics, giriş-çıkış analytics gerekiyor. Hangi sistem daha çok kullanılıyor? Cihaz uptime'ı nedir? Peak hours analizi nasıl yapılmalı? Donanım ROI nasıl hesaplanmalı? Salon sahibine hangi insights sunulmalı? Predictive maintenance analytics gerekli mi?

🔧 EK PROMPT'LAR - DONANIM ÖZEL DURUMLAR

PROMPT 26: RASPBERRY PI NETWORK VE GÜVENLİK
------------------------------------------
Raspberry Pi'lar salon WiFi'sine bağlanacak ama salon WiFi'si güvenilir olmayabilir. Network kesintileri, güvenlik açıkları, unauthorized access nasıl önlenmeli? VPN gerekli mi? Local caching nasıl yapılmalı? Offline mode nasıl çalışmalı? Network recovery stratejisi nedir?

PROMPT 27: QR KOD GÜVENLİĞİ VE ANTI-FRAUD
----------------------------------------
QR kodlar kopyalanabilir, sahte QR kodlar oluşturulabilir. QR kod encryption, expiration, one-time use nasıl implement edilmeli? Anti-fraud detection nasıl çalışmalı? Suspicious activity nasıl tespit edilir? QR kod güvenlik best practices neler?

PROMPT 28: KART SİSTEMİ ENTEGRASYONU
----------------------------------
Salon sahibi kart sistemi isterse hangi kart okuyucular desteklenmeli? RFID, NFC, magnetic stripe compatibility nasıl sağlanmalı? Third-party kart sistemi API entegrasyonu nasıl yapılmalı? Hybrid mode (QR + kart) nasıl çalışmalı? Fallback scenarios neler?

🔧 EK PROMPT'LAR - RASPBERRY PI ÖZEL DURUMLAR

PROMPT 29: RASPBERRY PI PYTHON KODU ANALİZİ VE ÖĞRENİM
----------------------------------------------------
Raspberry Pi'da çalışan Python kodumu yapay zeka yazdı ve ben Python bilmiyorum. Kod şu anda çalışıyor ama nasıl çalıştığını anlamıyorum. qr_turnstile_control.py dosyasında tkinter GUI, API çağrıları, GPIO kontrolleri var. TURNSTILE_ENABLED = False olarak ayarlanmış, yani turnike bağlantısı henüz yapılmamış.

Şu anda kurduğum sistem hakkında bilgim var ama Python, Raspberry Pi GPIO, tkinter, Linux sistem yönetimi konusunda deneyimim yok. Python nasıl çalışır? GPIO pinleri nedir, nasıl kontrol edilir? Tkinter GUI nasıl çalışır? Linux'ta servis olarak çalıştırma nasıl yapılır? Raspberry Pi'da debugging nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 30: TURNİKE DONANIM BAĞLANTISI VE KABLOLAMA
------------------------------------------------
Şu anda kodda TURNSTILE_ENABLED = False ve turnike fiziksel olarak bağlı değil. Raspberry Pi'ya turnike nasıl bağlanır? GPIO pin 18 kullanılacak ama hangi kablolar gerekli? Röle modülü gerekli mi? Güç kaynağı nasıl olmalı? Turnike voltajı nedir? Güvenlik önlemleri neler?

Şu anda kurduğum sistem hakkında bilgim var ama elektronik, kablolama, GPIO wiring konusunda deneyimim yok. GPIO pinleri nasıl çalışır? Röle modülü nedir, neden gerekli? Elektriksel güvenlik nasıl sağlanır? Turnike çeşitleri neler? Hangi turnike modelleri uyumlu?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 31: RASPBERRY PI PRODUCTION DEPLOYMENT VE YÖNETİM
------------------------------------------------------
Şu anda Raspberry Pi'yı manuel başlatıyorum (cd qr_turnstile_new, source venv/bin/activate, python qr_turnstile_control.py). Bu production için uygun değil. Sistem boot'ta otomatik başlamalı, crash olursa restart olmalı, remote monitoring olmalı.

Şu anda kurduğum sistem hakkında bilgim var ama Linux systemd, service management, remote monitoring konusunda deneyimim yok. Systemd service nedir? Auto-start nasıl yapılır? Process monitoring nasıl çalışır? Remote access nasıl sağlanır? Log management nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 32: RASPBERRY PI NETWORK VE GÜVENLİK YÖNETİMİ
--------------------------------------------------
Raspberry Pi salon WiFi'sine bağlanacak ama salon WiFi'si güvenilir olmayabilir. Şu anda API_URL = "https://api.gymkod.com/api/Member/scannumber" hard-coded. Network kesintilerinde offline mode yok. VPN, firewall, SSH güvenliği yapılandırılmamış.

Şu anda kurduğum sistem hakkında bilgim var ama Linux networking, security, VPN, firewall konusunda deneyimim yok. WiFi konfigürasyonu nasıl yapılır? SSH güvenliği nasıl sağlanır? VPN nedir, nasıl kurulur? Offline mode nasıl implement edilir? Network monitoring nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 33: RASPBERRY PI MASS DEPLOYMENT STRATEJİSİ
------------------------------------------------
Şu anda tek Raspberry Pi manuel kuruyorum ama Denizli'deki tüm salonlar için ölçeklenmeli. Her salon için ayrı ayrı kurulum yapmak imkansız. Image cloning, remote configuration, bulk deployment gerekiyor.

Şu anda kurduğum sistem hakkında bilgim var ama Raspberry Pi imaging, mass deployment, remote configuration management konusunda deneyimim yok. SD kart image nasıl oluşturulur? Bulk configuration nasıl yapılır? Remote provisioning nedir? Device management platform gerekli mi?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.
