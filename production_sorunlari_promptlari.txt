PRODUCTION-READY SORUNLARI İÇİN PROMPT LİSTESİ
==============================================

PROJE DURUMU: 1.5 yıldır tek spor salonunda sorunsuz çalışıyor. Hedef: Denizli'deki tüm spor salonlarına yayılmak için "tak-çalıştır" sistemi oluşturmak.

KRİTİK ÖNCELİK SIRASI:
1. Güvenlik & Altyapı (Prompt 1-8) - HEMEN
2. Ölçeklenebilirlik (Prompt 9-14) - KISA VADELİ
3. Operasyonel Sürdürülebilirlik (Prompt 15-20) - ORTA VADELİ
4. Satış Hazırlığı (Prompt 21-25) - UZUN VADELİ

🔥 KRİTİK ÖNCELİK - GÜVENLİK & ALTYAPI

PROMPT 1: PRODUCTION CONNECTION STRING GÜVENLİĞİ
----------------------------------------------
Sistemim 1.5 yıldır tek salonda sorunsuz çalışıyor ama şimdi Denizli'deki tüm salonlara yayılacak. GymContext.cs'de connection string hard-coded: "Server=localhost;Database=GymProject;Trusted_Connection=true;Encrypt=False". Appsettings.json'da production şifreleri açık görünüyor. Tek veritabanında multi-tenant yapı kullanacağım (CompanyID ile tenant separation).

Şu anda kurduğum sistem hakkında bilgim var ama production güvenliği konusunda deneyimim yok. Connection string güvenliği nasıl çalışır, neden önemli, gerçek hayatta nasıl uygulanır? Environment variables, Docker secrets, configuration management gibi yaklaşımların avantaj/dezavantajları neler? Ücretsiz veya düşük maliyetli çözümler var mı? Raspberry Pi cihazlarından API'ye güvenli bağlantı nasıl sağlanır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 2: MULTI-TENANT DBCONTEXT VE TENANT ISOLATION
---------------------------------------------------
Tek salonda çalışan sistemim şimdi çoklu salon yapısına geçecek. GymContext DI'da kayıtlı değil, OnConfiguring'de manuel connection string var. Tek veritabanında CompanyID ile tenant separation yapacağım. Her API isteğinde hangi salon için geldiğini anlayıp o salon'un verilerine erişim sağlamalı.

Şu anda kurduğum sistem hakkında bilgim var ama multi-tenant architecture konusunda deneyimim yok. Tenant isolation nasıl çalışır, neden kritik, gerçek hayatta nasıl implement edilir? CompanyContext pattern'i nedir, nasıl çalışır? DbContext DI registration'ın faydaları neler? Tenant data leakage nasıl önlenir? Row-level security nedir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 3: PRODUCTION CORS GÜVENLİK AÇIĞI
---------------------------------------
Development'ta AllowAnyOrigin() kullanıyorum ama şimdi gerçek müşterilere satış yapacağım. Her salon kendi domain'inden erişecek (salon1.gymkod.com, salon2.gymkod.com gibi).

Şu anda kurduğum sistem hakkında bilgim var ama CORS güvenliği konusunda deneyimim yok. CORS nedir, neden önemli, nasıl çalışır? AllowAnyOrigin() neden tehlikeli? Subdomain bazlı CORS nasıl yönetilir? Wildcard domain'ler güvenli mi? Production CORS policy'si nasıl tasarlanır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 4: PRODUCTION JWT TOKEN STRATEJİSİ
----------------------------------------
Tek salonda 2 dakikalık token süresi sorun değildi ama şimdi çoklu salon ortamında kullanıcı deneyimi kritik. SecurityKey açık, token rotation yok. Salon çalışanları gün boyu sistemi kullanacak, sürekli login olmak zorunda kalmamalı.

Şu anda kurduğum sistem hakkında bilgim var ama JWT security best practices konusunda deneyimim yok. JWT token lifecycle nasıl çalışır? Refresh token rotation nedir, neden önemli? Token süresi nasıl belirlenir? SecurityKey nasıl güvenli tutulur? Multi-tenant ortamda token management nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 5: RASPBERRY PI VE QR/KART SİSTEMİ RATE LİMİTİNG
-----------------------------------------------------
Tek salonda rate limiting sorun değildi ama şimdi her salonda Raspberry Pi cihazları sürekli QR kod okutacak. Ayrıca kart sistemi entegrasyonu da olacak. Raspberry Pi'lar sürekli /api/member/scannumber endpoint'ine istek atacak. Mevcut limit 10 saniyede 1 istek ama yoğun saatlerde çoklu giriş olabilir. IoT cihazları için rate limiting nasıl optimize edilmeli? QR vs kart sistemi için farklı limitler gerekli mi? Device-based rate limiting nasıl yapılmalı?

PROMPT 6: MULTI-TENANT CACHE STRATEJİSİ
--------------------------------------
Tek salonda cache sorun değildi ama şimdi her salon için ayrı cache gerekecek. MaxCacheSize 10.000 ama 50+ salon olunca yetersiz kalacak. Her salon farklı data pattern'ine sahip olacak. Cache isolation nasıl sağlanmalı? Salon bazlı cache eviction policy nasıl olmalı? Memory'yi adil paylaştırma stratejisi nedir? Redis gibi external cache gerekli mi?

PROMPT 7: MULTI-TENANT DI LIFETIME YÖNETİMİ
------------------------------------------
Tek salonda SingleInstance DAL'lar sorun değildi ama şimdi her salon için ayrı context gerekecek. DAL'lar SingleInstance olunca tenant isolation bozulabilir. Memory leak riski var. Multi-tenant ortamda DI lifetime stratejisi nasıl olmalı? Tenant-scoped service'ler nasıl implement edilmeli? Performance ve isolation dengesini nasıl kurabilirim?

PROMPT 8: PRODUCTION DATABASE İNDEX STRATEJİSİ
--------------------------------------------
Tek salonda query performance sorun değildi ama şimdi çoklu salon ortamında CompanyID filtreleri kritik olacak. Her sorgu CompanyID ile filtrelenmeli. Mevcut indexlerde CompanyID composite indexleri eksik olabilir. Multi-tenant query pattern'leri için hangi indexler şart? Performance monitoring nasıl yapılmalı? Index maintenance stratejisi nedir?

⚡ KISA VADELİ ÖNCELİK - ÖLÇEKLENEBİLİRLİK

PROMPT 9: SALON ÇALIŞANLARI İÇİN FRONTEND OPTİMİZASYONU
------------------------------------------------------
Tek salonda bundle size sorun değildi ama şimdi farklı internet hızlarında çalışan salonlar olacak. 15MB bundle çok büyük, yavaş internet'te açılmayabilir. Salon çalışanları sabırsız, hızlı açılması gerekiyor. Lazy loading, code splitting nasıl implement edilmeli? Salon bazlı feature'lar nasıl ayrılmalı? PWA özellikleri gerekli mi?

PROMPT 10: UZUN SÜRE AÇIK KALACAK SİSTEM İÇİN MEMORY YÖNETİMİ
-----------------------------------------------------------
Tek salonda günlük restart sorun değildi ama şimdi salonlar sistemi 7/24 açık tutacak. Memory leak'ler zamanla birikecek. Salon çalışanları teknik bilgili değil, restart yapamayacaklar. Frontend'te subscription cleanup, backend'te memory monitoring nasıl yapılmalı? Otomatik memory cleanup stratejisi nedir?

PROMPT 11: RASPBERRY PI SETUP VE DONANIM YÖNETİMİ
-----------------------------------------------
Şimdi her yeni salon için Raspberry Pi kurulumu, QR kod okuyucu konfigürasyonu, kart sistemi entegrasyonu manuel yapıyorum. Bu ölçeklenemez. Raspberry Pi'ların otomatik konfigürasyonu, remote management, firmware update'leri nasıl yapılmalı? QR vs kart sistemi seçimi nasıl yönetilmeli? Device monitoring ve troubleshooting nasıl olmalı? Salon sahibi teknik bilgili değilse nasıl destek verilmeli?

PROMPT 12: QR vs KART SİSTEMİ FİYATLANDIRMA STRATEJİSİ
--------------------------------------------------
Tek salonda QR sistem yetiyordu ama şimdi salon sahipleri "QR mu kart mı?" diye soracak, ikisini de isteyebilir. QR sistemi (sadece Raspberry Pi), kart sistemi (ek donanım), hibrit sistem (her ikisi) için farklı fiyatlandırma gerekiyor. Donanım maliyetleri, kurulum, maintenance nasıl fiyatlandırılmalı? Feature-based pricing nasıl yapılmalı? Upgrade/downgrade senaryoları nasıl yönetilmeli?

PROMPT 13: SALON PERFORMANS MONİTORİNG VE ANALİTİK
------------------------------------------------
Tek salonda manuel takip yetiyordu ama şimdi tüm salonları aynı anda izlemem gerekiyor. Hangi salon ne kadar kullanıyor, performance sorunları nerede, hangi feature'lar popüler? Salon dashboard'u nasıl olmalı? Admin panel'de tüm salonları nasıl izleyebilirim? Usage analytics nasıl toplanmalı?

PROMPT 14: KOLAY DEPLOYMENT VE GÜNCELLEME SİSTEMİ
-----------------------------------------------
Tek salonda manuel deployment sorun değildi ama şimdi tüm salonlara aynı anda güncelleme göndermem gerekecek. Zero-downtime deployment, rollback capability, A/B testing gerekiyor. Docker containerization nasıl yapılmalı? CI/CD pipeline nasıl kurulmalı? Blue-green deployment stratejisi nedir?

📊 ORTA VADELİ ÖNCELİK - OPERASYONEL SÜRDÜRÜLEBİLİRLİK

PROMPT 15: 7/24 UPTIME VE HEALTH MONİTORİNG
-----------------------------------------
Tek salonda downtime sorun değildi ama şimdi salonlar 7/24 çalışacak. Sistem çökerse salon işleri durur. Proactive monitoring, alerting, auto-healing gerekiyor. Health check'ler nasıl kurulmalı? Database, cache, API health nasıl izlenmeli? Incident response planı nedir? Uptime SLA nasıl garanti edilir?

PROMPT 16: CENTRALIZED LOGGİNG VE ERROR TRACKİNG
----------------------------------------------
Tek salonda local log'lar yetiyordu ama şimdi tüm salonların log'larını merkezi izlemem gerekiyor. Hangi salonda ne hata oluyor, performance sorunları nerede? Structured logging, correlation ID, centralized log management nasıl kurulmalı? ELK Stack, Application Insights'tan hangisi uygun? Error alerting nasıl çalışmalı?

PROMPT 17: OTOMATIK BACKUP VE DISASTER RECOVERY
---------------------------------------------
Tek salonda manuel backup yetiyordu ama şimdi her salon için otomatik backup gerekiyor. Salon verileri kaybolursa iş durur. Point-in-time recovery, cross-region backup, disaster recovery planı nasıl olmalı? Backup retention policy nedir? RTO/RPO değerleri nasıl belirlenmeli? Backup test stratejisi nedir?

PROMPT 18: SECURITY COMPLIANCE VE AUDIT
-------------------------------------
Tek salonda güvenlik sorun değildi ama şimdi müşteri verileri için KVKK compliance gerekiyor. Data encryption, audit logging, access control, privacy policy implementation nasıl yapılmalı? GDPR/KVKV requirements neler? Security audit nasıl geçilir? Penetration testing gerekli mi?

PROMPT 19: LOAD BALANCİNG VE AUTO-SCALİNG
---------------------------------------
Tek instance ile başlayacağım ama büyüdükçe horizontal scaling gerekecek. Peak saatlerde (akşam 18-21) tüm salonlar aynı anda yoğun kullanım yapacak. Load balancer, auto-scaling, session management nasıl kurulmalı? Azure/AWS'de hangi servisler kullanılmalı? Cost optimization nasıl yapılmalı?

PROMPT 20: PERFORMANCE OPTİMİZASYON VE QUERY TUNİNG
------------------------------------------------
Tek salonda query performance sorun değildi ama şimdi çoklu salon ortamında N+1 query, eager loading, pagination sorunları çıkabilir. Database query optimization, Entity Framework best practices, connection pooling nasıl yapılmalı? Query execution plan analysis nasıl yapılır? Performance bottleneck'leri nasıl tespit edilir?

🎯 UZUN VADELİ ÖNCELİK - SATIŞ HAZIRLIĞI

PROMPT 21: IOT CİHAZ YÖNETİMİ VE REMOTE SUPPORT
----------------------------------------------
Tek salonda fiziksel erişim kolaydı ama şimdi Denizli'deki tüm salonlarda Raspberry Pi'lar olacak. Remote monitoring, firmware update, troubleshooting, device health check nasıl yapılmalı? Cihaz çökerse salon girişleri durur. IoT device management platform gerekli mi? Remote access nasıl güvenli yapılır? Preventive maintenance nasıl planlanır?

PROMPT 22: FİZİKSEL KURULUM VE SALON ONBOARDING
--------------------------------------------
Yeni salon nasıl sisteme dahil edilecek? Raspberry Pi kurulumu, QR kod okuyucu montajı, kart sistemi entegrasyonu, network konfigürasyonu, staff training nasıl yapılmalı? Salon sahibi teknik bilgili değilse kurulum nasıl basitleştirilmeli? Remote support nasıl verilmeli? Troubleshooting guide nasıl hazırlanmalı? Go-live checklist'i nedir?

PROMPT 23: BILLING VE PAYMENT INTEGRATION
----------------------------------------
Şimdi aylık/yıllık subscription model gerekiyor. Otomatik billing, payment processing, invoice generation, dunning management nasıl yapılmalı? Stripe, PayTR gibi payment gateway'lerden hangisi uygun? Pricing tiers nasıl yönetilmeli? Churn prevention stratejisi nedir?

PROMPT 24: LEGAL VE COMPLIANCE HAZIRLIĞI
---------------------------------------
SaaS business için terms of service, privacy policy, SLA agreements gerekiyor. KVKV compliance, data processing agreements, liability limitations nasıl hazırlanmalı? Legal review süreci nedir? Compliance audit nasıl geçilir? Insurance requirements neler?

PROMPT 25: DONANIM VE YAZILIM ENTEGRASYON ANALİTİĞİ
-------------------------------------------------
Salon sahipleri için QR vs kart kullanım istatistikleri, cihaz performance metrics, giriş-çıkış analytics gerekiyor. Hangi sistem daha çok kullanılıyor? Cihaz uptime'ı nedir? Peak hours analizi nasıl yapılmalı? Donanım ROI nasıl hesaplanmalı? Salon sahibine hangi insights sunulmalı? Predictive maintenance analytics gerekli mi?

🔧 EK PROMPT'LAR - DONANIM ÖZEL DURUMLAR

PROMPT 26: RASPBERRY PI NETWORK VE GÜVENLİK
------------------------------------------
Raspberry Pi'lar salon WiFi'sine bağlanacak ama salon WiFi'si güvenilir olmayabilir. Network kesintileri, güvenlik açıkları, unauthorized access nasıl önlenmeli? VPN gerekli mi? Local caching nasıl yapılmalı? Offline mode nasıl çalışmalı? Network recovery stratejisi nedir?

PROMPT 27: QR KOD GÜVENLİĞİ VE ANTI-FRAUD
----------------------------------------
QR kodlar kopyalanabilir, sahte QR kodlar oluşturulabilir. QR kod encryption, expiration, one-time use nasıl implement edilmeli? Anti-fraud detection nasıl çalışmalı? Suspicious activity nasıl tespit edilir? QR kod güvenlik best practices neler?

PROMPT 28: KART SİSTEMİ ENTEGRASYONU
----------------------------------
Salon sahibi kart sistemi isterse hangi kart okuyucular desteklenmeli? RFID, NFC, magnetic stripe compatibility nasıl sağlanmalı? Third-party kart sistemi API entegrasyonu nasıl yapılmalı? Hybrid mode (QR + kart) nasıl çalışmalı? Fallback scenarios neler?
