PRODUCTION-READY SORUNLARI İÇİN PROMPT LİSTESİ (ÇAKIŞMASIZ SIRALAMA)
====================================================================

PROJE DURUMU: 1.5 yıldır tek spor salonunda sorunsuz çalışıyor. Hedef: Den<PERSON><PERSON>'deki tüm spor salonlarına yayılmak için "tak-çalıştır" sistemi oluşturmak.

⚠️ ÇAKIŞMASIZ SIRALAMA: Her prompt bir öncekinin üzerine inşa edilir. <PERSON><PERSON><PERSON><PERSON>!

🔥 GRUP 1: TEMEL ALTYAPI (SIRALI - ÇAKIŞMA YOK) (1-3)
==================================================

PROMPT 1: MANUEL ENVIRONMENT MANAGEMENT VE CONNECTION STRING GÜVENLİĞİ (HAYATİ!)
------------------------------------------------------------------------
3 <USER> <GROUP>ıyorum: 1) Local test, 2) Staging (staging.gymkod.com), 3) Production (admin.gymkod.com). 
Her environment geçişinde GymContext.cs'deki connection string'i manuel değiştiriyorum:
- Staging: "Server=localhost;User Id=sa;Password=************;Database=Staging;Trusted_Connection=false;Encrypt=False"
- Production: "Server=localhost;User Id=sa;Password=************;Database=GymProject;Trusted_Connection=false;Encrypt=False"
Ayrıca Program.cs'yi de manuel düzenliyorum. Bu süreç hata riski yüksek, ölçeklenebilir değil ve şifreler kod içinde görünüyor!

Şu anda kurduğum sistem hakkında bilgim var ama environment-based configuration konusunda deneyimim yok. 
Environment variables nasıl çalışır? appsettings.{Environment}.json pattern'i nedir? 
Configuration management nasıl otomatikleştirilir? DevOps best practices neler? GitHub'da şifre nasıl gizlenir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 2: ENVIRONMENT BAZLI CORS VE PROGRAM.CS KONFİGÜRASYONU (HAYATİ!)
--------------------------------------------------------------------
Program.cs'de AllowAnyOrigin() kullanıyorum - bu herhangi bir website'in API'mi kullanabileceği anlamına geliyor! 
3 environment'ım var: Local, Staging (staging.gymkod.com), Production (admin.gymkod.com).
Her environment için farklı CORS policy gerekiyor ama şu anda hepsinde aynı kod var. PROMPT 1'de environment configuration'ı hallettikten sonra şimdi CORS'u environment bazlı yapacağım.

Şu anda kurduğum sistem hakkında bilgim var ama environment-based CORS konusunda deneyimim yok.
Environment bazlı CORS nasıl yapılır? staging.gymkod.com ve admin.gymkod.com için 
güvenli CORS policy'si nasıl tasarlanır? Development vs Production CORS farkları neler?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 3: DBCONTEXT DI KAYDI VE MULTI-TENANT ISOLATION (HAYATİ!)
--------------------------------------------------------------
GymContext DI'da kayıtlı değil, OnConfiguring'de manuel connection string var. PROMPT 1-2'de environment configuration'ı hallettim, şimdi DbContext'i DI'a kaydedeceğim. 
Sonra çoklu salon yapısına geçeceğim. Tek veritabanında CompanyID ile tenant separation yapacağım. 
Şu anda her API isteğinde hangi salon için geldiğini anlama mekanizmam yok. Bir salon diğer salonun verilerini görebilir - bu felaket olur!

Şu anda kurduğum sistem hakkında bilgim var ama DbContext DI registration ve multi-tenant isolation konusunda deneyimim yok. 
DbContext DI'a nasıl kaydedilir? Tenant isolation nasıl çalışır? CompanyContext pattern'i nedir? Row-level security nasıl implement edilir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

🚨 GRUP 2: GÜVENLİK KATMANI (BAĞIMSIZ) (4-6)
==========================================

PROMPT 4: JWT TOKEN GÜVENLİK SORUNLARI
------------------------------------
SecurityKey appsettings.json'da açık, token rotation yok. 2 dakikalık token süresi çok kısa - salon çalışanları sürekli login olmak zorunda kalacak. 
Çoklu salon ortamında kullanıcı deneyimi kritik. Bu prompt bağımsız, önceki prompt'larla çakışmaz.

Şu anda kurduğum sistem hakkında bilgim var ama JWT security best practices konusunda deneyimim yok. 
JWT token lifecycle nasıl çalışır? Refresh token rotation nedir? Token süresi nasıl belirlenir? SecurityKey nasıl güvenli tutulur?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 5: API INPUT VALİDATİON VE GÜVENLİK AÇIKLARI
-------------------------------------------------
Controller'larda model validation eksik. MemberController, MembershipController gibi endpoint'lerde input validation yok. 
SQL injection, XSS attack riski var. FluentValidation var ama controller seviyesinde uygulanmamış. Bu prompt bağımsız, diğer prompt'larla çakışmaz.

Şu anda kurduğum sistem hakkında bilgim var ama API security konusunda deneyimim yok. 
Input validation nasıl yapılır? SQL injection nasıl önlenir? XSS protection nedir? Model binding security nasıl sağlanır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 6: MOBİL APP GÜVENLİK VE NETWORK AÇIĞI
--------------------------------------------
Mobile app'te baseUrl = 'http://*************:5165/api/' hard-coded IP var ve HTTP kullanıyor (HTTPS değil). 
Production'da bu IP çalışmayacak ve güvenlik riski oluşturuyor. Network kesintilerinde turnike çalışmayacak, offline mode yok. Bu prompt bağımsız.

Şu anda kurduğum sistem hakkında bilgim var ama mobile app network management konusunda deneyimim yok. 
Environment-based API URL nasıl yapılır? HTTPS nasıl implement edilir? Offline mode nasıl çalışır? Mobile app security best practices neler?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

⚡ GRUP 3: PERFORMANCE KATMANI (SIRALI) (7-9)
==========================================

PROMPT 7: DATABASE İNDEX EKSİKLİĞİ PERFORMANS ÇÖKÜŞÜ
---------------------------------------------------
Çoklu salon ortamında her sorgu CompanyID ile filtrelenmeli ama mevcut indexlerde CompanyID composite indexleri eksik olabilir. 
50+ salon olunca sorgular çok yavaşlayacak, sistem kullanılamaz hale gelecek. PROMPT 3'te tenant isolation hallettikten sonra şimdi performance optimize edeceğim.

Şu anda kurduğum sistem hakkında bilgim var ama database indexing konusunda deneyimim yok. 
Index nedir, neden önemli? Composite index nasıl çalışır? Multi-tenant query pattern'leri için hangi indexler şart? Query performance nasıl ölçülür?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 8: DI LIFETIME MEMORY LEAK RİSKİ
-------------------------------------
AutofacBusinessModule'de DAL sınıfları SingleInstance olarak kayıtlı. Bu yaklaşım memory leak'e neden olabilir ve çoklu salon ortamında sistem çökebilir. 
PROMPT 3'te DbContext DI'ı hallettikten sonra şimdi diğer service'lerin lifetime'larını optimize edeceğim.

Şu anda kurduğum sistem hakkında bilgim var ama DI lifetime management konusunda deneyimim yok. 
SingleInstance vs Scoped vs Transient farkları neler? Memory leak nasıl oluşur? DbContext lifetime neden önemli? Multi-tenant ortamda DI nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 9: N+1 QUERY VE PERFORMANCE BOTTLENECK'LER
------------------------------------------------
EfUserDal'da N+1 query problemi var. Eager loading eksik. Database connection pooling yok. 
PROMPT 7-8'de indexing ve DI'ı hallettikten sonra şimdi query optimization yapacağım.

Şu anda kurduğum sistem hakkında bilgim var ama query optimization konusunda deneyimim yok. 
N+1 query problemi nedir? Eager loading nasıl yapılır? Connection pooling nasıl çalışır? Query performance nasıl optimize edilir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.
