PRODUCTION-READY SORUNLARI İÇİN PROMPT LİSTESİ
==============================================

PROMPT 1: VERİTABANI CONNECTION STRING GÜVENLİĞİ
-----------------------------------------------
<PERSON><PERSON><PERSON> backend kısmında GymContext.cs dosyasında connection string hard-coded olarak yazılmış durumda. OnConfiguring metodunda "Server=localhost;Database=GymProject;Trusted_Connection=true;Encrypt=False" şeklinde doğrudan kod içinde yer alıyor. Ayrıca appsettings.json dosyalarında da production şifreleri açık şekilde görünüyor. Bu durum 1000 spor salonu ve 10.000+ kullanıc<PERSON> için ciddi güvenlik riski oluşturuyor. Bu connection string yönetimi nasıl güvenli hale getirilmeli? Environment variables, Azure Key Vault gibi seçenekler arasında en uygun çözüm hangisi olur?

PROMPT 2: ENTITY FRAMEWORK DBCONTEXT DI KAYDI
---------------------------------------------
Şu anda GymContext sınıfım DbContext'ten inherit ediyor ama Program.cs'de DI container'a kayıtlı değil. OnConfiguring metodunda connection string'i manuel olarak set ediyorum. Bu yaklaşım connection pooling, lifetime management ve performance açısından sorunlu. DbContext'i DI container'a nasıl doğru şekilde kaydetmeliyim? AddDbContext mi AddDbContextPool mi kullanmalıyım? Connection pooling ayarları nasıl optimize edilmeli?

PROMPT 3: CORS GÜVENLİK AÇIĞI
-----------------------------
Program.cs dosyamda CORS konfigürasyonu şu şekilde: app.UseCors(builder => builder.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod()). Bu konfigürasyon development için uygun ama production'da ciddi güvenlik açığı oluşturuyor. 1000 salon için güvenli CORS politikası nasıl oluşturulmalı? Environment bazlı CORS ayarları nasıl yapılmalı?

PROMPT 4: JWT TOKEN GÜVENLİK SORUNLARI
--------------------------------------
JWT token konfigürasyonumda SecurityKey appsettings.json'da açık şekilde duruyor. AccessTokenExpiration 2 dakika olarak ayarlanmış ki bu çok kısa ve kullanıcı deneyimini bozuyor. RefreshTokenExpiration 30 dakika. Token rotation mekanizması yok. 10.000+ kullanıcı için JWT token güvenliği nasıl optimize edilmeli? Token süreleri nasıl ayarlanmalı? Refresh token rotation nasıl implement edilmeli?

PROMPT 5: RATE LİMİTİNG KAPASITESI
----------------------------------
AspNetCoreRateLimit konfigürasyonumda genel kurallar dakikada 100 istek, saatte 1000 istek olarak ayarlanmış. Bu limitler 10.000+ kullanıcı için çok düşük. Ayrıca endpoint bazlı limitler de çok kısıtlayıcı (örneğin member/getall dakikada 30 istek). Bu rate limiting ayarları nasıl ölçeklendirilmeli? Hangi endpoint'ler için hangi limitler uygun olur?

PROMPT 6: CACHE PERFORMANS SORUNLARI
------------------------------------
MultiTenantCacheManager'da MaxCacheSize 10.000 olarak ayarlanmış. 1000 salon için bu çok düşük. Cache hit ratio'su düşük olabilir. Memory pressure altında cache'ler silinebilir. Cache invalidation stratejisi tenant bazlı ama performance metrics'e göre optimize edilmemiş. 1000 salon ve 10.000+ kullanıcı için cache stratejisi nasıl optimize edilmeli?

PROMPT 7: DEPENDENCY INJECTION LIFETIME SORUNLARI
--------------------------------------------------
AutofacBusinessModule'de DAL sınıfları SingleInstance olarak kayıtlı. Bu yaklaşım memory leak'e neden olabilir ve DbContext lifetime'ı ile uyumsuz. Ayrıca business service'ler de SingleInstance. Multi-tenant ortamda bu yaklaşım sorunlu. DI lifetime'ları nasıl düzeltilmeli? Hangi servisler singleton, hangileri scoped olmalı?

PROMPT 8: DATABASE İNDEX EKSİKLİKLERİ
-------------------------------------
Migration dosyalarımda performans indexleri var ama mevcut tablolarda eksiklikler olabilir. Multi-tenant sorgular için composite indexler, foreign key'lerde indexler, frequently queried column'larda indexler eksik olabilir. 10.000+ kullanıcı için database performance'ı nasıl optimize edilmeli? Hangi indexler kritik öneme sahip?

PROMPT 9: FRONTEND BUNDLE SIZE SORUNU
-------------------------------------
Angular.json'da production build için bundle size limiti 15MB olarak ayarlanmış ki bu çok yüksek. Tüm component'ler tek modülde tanımlı, lazy loading yok. Tree shaking optimize edilmemiş. 10.000+ kullanıcı için frontend performance nasıl optimize edilmeli? Lazy loading nasıl implement edilmeli?

PROMPT 10: MEMORY LEAK RİSKLERİ
-------------------------------
Angular component'lerde subscription'lar unsubscribe edilmiyor olabilir. Event listener'lar temizlenmiyor. Backend'de singleton service'ler memory accumulation'a neden olabilir. Cache'ler sürekli büyüyor. Memory leak'leri nasıl önlenebilir? Memory monitoring nasıl yapılmalı?

PROMPT 11: HEALTH CHECK EKSİKLİKLERİ
------------------------------------
Şu anda sadece basit bir /api/health endpoint'im var. Database connectivity, cache health, external service health check'leri yok. Application insights yok. Uptime monitoring yok. 1000 salon için comprehensive health monitoring nasıl kurulmalı? Hangi metrics izlenmeli? Alert sistemi nasıl kurulmalı?

PROMPT 12: STRUCTURED LOGGİNG EKSİKLİKLERİ
------------------------------------------
FileLoggerService ile basic logging yapıyorum ama structured logging yok. Correlation ID yok. Log levels environment'a göre ayarlanmamış. Performance metrics loglanmıyor. Centralized logging yok. 10.000+ kullanıcı için logging stratejisi nasıl olmalı? ELK Stack, Application Insights gibi çözümlerden hangisi uygun?

PROMPT 13: ERROR TRACKING VE MONİTORİNG
---------------------------------------
ExceptionMiddleware ile basic error handling var ama centralized error tracking yok. Error metrics yok. User impact analysis yok. Real-time alerting yok. Production'da hataları nasıl track etmeliyim? Sentry, Application Insights gibi çözümlerden hangisi uygun? Error rate thresholds nasıl belirlenmeli?

PROMPT 14: CONTAİNERİZATİON EKSİKLİĞİ
------------------------------------
Projem Docker konfigürasyonu yok. Kubernetes deployment yok. Container orchestration yok. Multi-stage build yok. 1000 salon için containerization stratejisi nasıl olmalı? Docker Compose mi Kubernetes mi kullanmalıyım? Container resource limits nasıl ayarlanmalı?

PROMPT 15: CI/CD PİPELİNE EKSİKLİĞİ
-----------------------------------
Otomatik build, test, deploy pipeline'ım yok. Environment management manuel. Blue-green deployment yok. Rollback stratejisi yok. 1000 salon için CI/CD pipeline nasıl kurulmalı? GitHub Actions, Azure DevOps, Jenkins'ten hangisi uygun? Deployment stratejisi nasıl olmalı?

PROMPT 16: LOAD BALANCİNG VE SCALİNG
-----------------------------------
Şu anda tek instance çalışıyor. Horizontal scaling planı yok. Load balancer yok. Auto-scaling yok. Session affinity yok. 10.000+ kullanıcı için load balancing nasıl kurulmalı? Azure Load Balancer, Application Gateway'den hangisi uygun? Auto-scaling metrics'leri neler olmalı?

PROMPT 17: DATABASE CONNECTION POOLİNG
-------------------------------------
EfEntityRepositoryBase'de her işlem için yeni DbContext instance'ı oluşturuluyor. Connection pooling optimize edilmemiş. Connection timeout ayarları yok. Retry policy yok. 10.000+ kullanıcı için database connection management nasıl optimize edilmeli? Connection pool size nasıl ayarlanmalı?

PROMPT 18: QUERY PERFORMANCE OPTİMİZASYONU
------------------------------------------
N+1 query problemi olabilir. Eager loading eksik. Pagination optimize edilmemiş. Query execution plan'ları analiz edilmemiş. 1000 salon için database query performance nasıl optimize edilmeli? Entity Framework query optimization best practices'leri neler?

PROMPT 19: SECURITY HEADERS EKSİKLİĞİ
------------------------------------
HTTP security headers yok. HSTS yok. Content Security Policy yok. X-Frame-Options yok. XSS protection yok. Production'da security headers nasıl implement edilmeli? OWASP security guidelines'larına uyum nasıl sağlanmalı?

PROMPT 20: BACKUP VE RECOVERY STRATEJİSİ
---------------------------------------
Database backup stratejim yok. Point-in-time recovery yok. Disaster recovery planı yok. Data retention policy yok. 1000 salon için backup ve recovery stratejisi nasıl olmalı? RTO ve RPO değerleri nasıl belirlenmeli?
