PRODUCTION-READY SORUNLARI İÇİN PROMPT LİSTESİ (ÖNEM SIRASINA GÖRE)
================================================================

PROJE DURUMU: 1.5 yıldır tek spor salonunda sorunsuz çalışıyor. Hedef: Deniz<PERSON>'deki tüm spor salonlarına yayılmak için "tak-çalıştır" sistemi oluşturmak.

⚠️ ÖNEM SIRASI: En kritik sorundan başlayarak sıralanmıştır. 1'den başlayın!

🔥 HAYATI ÖNCELİK - SİSTEM ÇÖKMEDEN ÖNCE (1-5)
============================================

PROMPT 1: MULTI-TENANT TENANT ISOLATION GÜVENLİĞİ (HAYATİ!)
--------------------------------------------------------
Sistemim 1.5 yıldır tek salonda sorunsuz çalışıyor ama şimdi çoklu salon yapısına geçecek. Tek veritabanında CompanyID ile tenant separation yapacağım. Şu anda her API isteğinde hangi salon için geldiğini anlama mekanizmam nasıl incelemeni istiyorum.. Bir salon diğer salonun verilerini görebilir mi?

Şu anda kurduğum sistem hakkında bilgim var ama multi-tenant data isolation konusunda deneyimim yok. Tenant isolation nasıl çalışır, neden kritik? Row-level security nedir? CompanyContext pattern'i nasıl implement edilir? Data leakage nasıl önlenir? Tenant resolution nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 2: PRODUCTION CONNECTION STRING GÜVENLİĞİ (HAYATİ!)
--------------------------------------------------------
GymContext.cs'de connection string hard-coded: "Server=localhost;Database=GymProject;Trusted_Connection=true;Encrypt=False". Appsettings.json'da production şifreleri açık görünüyor. Kod GitHub'a push olunca herkes database şifremi görebilir mi?

Şu anda kurduğum sistem hakkında bilgim var ama production güvenliği konusunda deneyimim yok. Connection string güvenliği nasıl çalışır, neden önemli? Environment variables, Docker secrets gibi yaklaşımların avantaj/dezavantajları neler? Ücretsiz çözümler var mı? GitHub'da şifre nasıl gizlenir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 3: PRODUCTION CORS GÜVENLİK AÇIĞI (HAYATİ!)
-------------------------------------------------
Development'ta AllowAnyOrigin() kullanıyorum - bu herhangi bir website'in API'mi kullanabileceği anlamına geliyor! Kötü niyetli kişiler API'mi abuse edebilir.

Şu anda kurduğum sistem hakkında bilgim var ama CORS güvenliği konusunda deneyimim yok. CORS nedir, neden önemli? AllowAnyOrigin() neden tehlikeli? Production CORS policy'si nasıl tasarlanır? Subdomain bazlı CORS nasıl yönetilir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 4: DI LIFETIME MEMORY LEAK RİSKİ (HAYATİ!)
-----------------------------------------------
AutofacBusinessModule'de DAL sınıfları SingleInstance olarak kayıtlı. Bu yaklaşım memory leak'e neden olabilir mi veya çoklu salon ortamında sistem çökebilir mi? Ayrıca DbContext lifetime'ı ile uyumlu mu inceler misin?

Şu anda kurduğum sistem hakkında bilgim var ama DI lifetime management konusunda deneyimim yok. SingleInstance vs Scoped vs Transient farkları neler? Memory leak nasıl oluşur? DbContext lifetime neden önemli? Multi-tenant ortamda DI nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 5: DATABASE İNDEX EKSİKLİĞİ PERFORMANS ÇÖKÜŞÜ (HAYATİ!)
------------------------------------------------------------
Çoklu salon ortamında her sorgu CompanyID ile filtrelenmeli ama mevcut indexlerde CompanyID composite indexleri eksik olabilir. 50+ salon olunca sorgular çok yavaşlayacak, sistem kullanılamaz hale gelecek.

Şu anda kurduğum sistem hakkında bilgim var ama database indexing konusunda deneyimim yok. Index nedir, neden önemli? Composite index nasıl çalışır? Multi-tenant query pattern'leri için hangi indexler şart? Query performance nasıl ölçülür?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

🚨 KRİTİK ÖNCELİK - SİSTEM STABIL OLMASI İÇİN (6-10)
=================================================

PROMPT 6: JWT TOKEN GÜVENLİK SORUNLARI
------------------------------------
SecurityKey appsettings.json'da açık, token rotation yok. 2 dakikalık token süresi çok kısa - salon çalışanları sürekli login olmak zorunda kalacak. Çoklu salon ortamında kullanıcı deneyimi kritik.

Şu anda kurduğum sistem hakkında bilgim var ama JWT security best practices konusunda deneyimim yok. JWT token lifecycle nasıl çalışır? Refresh token rotation nedir? Token süresi nasıl belirlenir? SecurityKey nasıl güvenli tutulur?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 7: RASPBERRY PI RATE LİMİTİNG SORUNU
------------------------------------------
Mevcut rate limiting 10 saniyede 1 istek ama yoğun saatlerde çoklu giriş olabilir. Raspberry Pi'lar sürekli /api/member/scannumber endpoint'ine istek atacak. Rate limiting çok kısıtlayıcı olunca turnike çalışmayacak.

Şu anda kurduğum sistem hakkında bilgim var ama IoT device rate limiting konusunda deneyimim yok. IoT cihazları için rate limiting neden farklı? Burst traffic nasıl handle edilir? Device-based rate limiting nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 8: MULTI-TENANT CACHE MEMORY SORUNU
-----------------------------------------
MaxCacheSize 10.000 ama 50+ salon olunca yetersiz kalacak. Her salon farklı data pattern'ine sahip olacak. Cache isolation sağlanmazsa bir salon diğerinin cache'ini bozabilir.

Şu anda kurduğum sistem hakkında bilgim var ama multi-tenant cache management konusunda deneyimim yok. Cache isolation nasıl çalışır? Memory pressure nasıl yönetilir? Cache eviction policy nedir? Redis vs in-memory cache farkları neler?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 9: FRONTEND BUNDLE SIZE PERFORMANS SORUNU
-----------------------------------------------
15MB bundle çok büyük, yavaş internet'te açılmayabilir. Salon çalışanları sabırsız, hızlı açılması gerekiyor. Tüm component'ler tek modülde tanımlı, lazy loading yok.

Şu anda kurduğum sistem hakkında bilgim var ama Angular performance optimization konusunda deneyimim yok. Bundle size neden önemli? Lazy loading nasıl çalışır? Code splitting nedir? Tree shaking nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 10: MEMORY LEAK RİSKLERİ
------------------------------
Salonlar sistemi 7/24 açık tutacak. Memory leak'ler zamanla birikecek. Salon çalışanları teknik bilgili değil, restart yapamayacaklar. Frontend'te subscription cleanup, backend'te memory monitoring eksik.

Şu anda kurduğum sistem hakkında bilgim var ama memory management konusunda deneyimim yok. Memory leak nasıl oluşur? Angular'da subscription cleanup nasıl yapılır? Backend'te memory monitoring nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

⚡ YÜKSEK ÖNCELİK - RASPBERRY PI DONANIM (11-15)
==============================================

PROMPT 11: RASPBERRY PI PYTHON KODU ANALİZİ VE ÖĞRENİM
----------------------------------------------------
Raspberry Pi'da çalışan Python kodumu yapay zeka yazdı ve ben Python bilmiyorum. Kod şu anda çalışıyor ama nasıl çalıştığını anlamıyorum. TURNSTILE_ENABLED = False olarak ayarlanmış, yani turnike bağlantısı henüz yapılmamış.

Şu anda kurduğum sistem hakkında bilgim var ama Python, Raspberry Pi GPIO, tkinter konusunda deneyimim yok. Python nasıl çalışır? GPIO pinleri nedir? Tkinter GUI nasıl çalışır? Linux'ta servis olarak çalıştırma nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 12: TURNİKE DONANIM BAĞLANTISI VE KABLOLAMA
------------------------------------------------
TURNSTILE_ENABLED = False ve turnike fiziksel olarak bağlı değil. GPIO pin 18 kullanılacak ama hangi kablolar gerekli? Röle modülü gerekli mi? Güç kaynağı nasıl olmalı? Turnike voltajı nedir?

Şu anda kurduğum sistem hakkında bilgim var ama elektronik, kablolama, GPIO wiring konusunda deneyimim yok. GPIO pinleri nasıl çalışır? Röle modülü nedir, neden gerekli? Elektriksel güvenlik nasıl sağlanır? Turnike çeşitleri neler?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 13: RASPBERRY PI PRODUCTION DEPLOYMENT
--------------------------------------------
Manuel başlatıyorum (cd qr_turnstile_new, source venv/bin/activate, python qr_turnstile_control.py). Bu production için uygun değil. Sistem boot'ta otomatik başlamalı, crash olursa restart olmalı.

Şu anda kurduğum sistem hakkında bilgim var ama Linux systemd, service management konusunda deneyimim yok. Systemd service nedir? Auto-start nasıl yapılır? Process monitoring nasıl çalışır? Remote access nasıl sağlanır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 14: RASPBERRY PI NETWORK GÜVENLİĞİ
----------------------------------------
Raspberry Pi salon WiFi'sine bağlanacak ama salon WiFi'si güvenilir olmayabilir. API_URL hard-coded. Network kesintilerinde offline mode yok. VPN, firewall, SSH güvenliği yapılandırılmamış.

Şu anda kurduğum sistem hakkında bilgim var ama Linux networking, security konusunda deneyimim yok. WiFi konfigürasyonu nasıl yapılır? SSH güvenliği nasıl sağlanır? VPN nedir? Offline mode nasıl implement edilir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 15: RASPBERRY PI MASS DEPLOYMENT
--------------------------------------
Tek Raspberry Pi manuel kuruyorum ama Denizli'deki tüm salonlar için ölçeklenmeli. Her salon için ayrı ayrı kurulum yapmak imkansız. Image cloning, remote configuration, bulk deployment gerekiyor.

Şu anda kurduğum sistem hakkında bilgim var ama Raspberry Pi imaging, mass deployment konusunda deneyimim yok. SD kart image nasıl oluşturulur? Bulk configuration nasıl yapılır? Remote provisioning nedir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.
